# api_test_script.py
# -*- coding: utf-8 -*-

import json
import urllib.request

# --- 配置 ---
BASE_URL = "http://127.0.0.1:5000/api/v1"
USER_CREDENTIALS = {
    "username": "testuser",
    "password": "password123"
}
DEVICE_DATA = {
    "nickname": "我的第一个ESP32设备",
    "board_model": "ESP32-WROOM-32"
}


def make_request(url, data=None, headers={}):
    """一个通用的请求函数"""
    req = urllib.request.Request(url, headers=headers)

    # 如果有data，说明是POST请求
    if data:
        req.data = json.dumps(data).encode('utf-8')
        req.add_header('Content-Type', 'application/json')

    try:
        with urllib.request.urlopen(req) as response:
            print(f"请求成功: {response.status} {response.reason}")
            response_body = response.read().decode('utf-8')
            return json.loads(response_body)
    except urllib.error.HTTPError as e:
        print(f"请求失败: {e.code} {e.reason}")
        print(f"错误详情: {e.read().decode('utf-8')}")
        return None


def run_test_flow():
    """执行完整的API测试流程"""
    access_token = None

    print("\n--- 1. 注册新用户 ---")
    register_url = f"{BASE_URL}/auth/register"
    make_request(register_url, data=USER_CREDENTIALS)

    print("\n--- 2. 登录并获取Token ---")
    login_url = f"{BASE_URL}/auth/login"
    login_response = make_request(login_url, data=USER_CREDENTIALS)

    if login_response and 'access_token' in login_response:
        access_token = login_response['access_token']
        print(f"成功获取Access Token: {access_token[:20]}...")
    else:
        print("登录失败，测试中止。")
        return

    # 准备带有认证的请求头
    auth_headers = {
        "Authorization": f"Bearer {access_token}"
    }

    print("\n--- 3. 注册新设备 (携带Token) ---")
    devices_url = f"{BASE_URL}/devices"
    make_request(devices_url, data=DEVICE_DATA, headers=auth_headers)

    print("\n--- 4. 获取设备列表 (携带Token) ---")
    get_devices_response = make_request(devices_url, headers=auth_headers)
    if get_devices_response is not None:
        print("成功获取设备列表:")
        print(json.dumps(get_devices_response, indent=2, ensure_ascii=False))

    print("\n--- 测试流程结束 ---")


if __name__ == "__main__":
    run_test_flow()