# run.py

import os
from app import create_app, db
from app.models import User, Device  # 导入模型以便SQLAlchemy能识别它们

# 使用开发环境配置创建应用
app = create_app(os.getenv('FLASK_CONFIG') or 'development')


@app.shell_context_processor
def make_shell_context():
    # 为flask shell提供上下文
    return dict(db=db, User=User, Device=Device)


if __name__ == '__main__':
    with app.app_context():
        # 这行代码会在应用首次运行时检查数据库文件和表是否存在，
        # 如果不存在，则会根据 models.py 的定义来创建它们。
        db.create_all()

    # 启动Flask开发服务器
    app.run(debug=True, host='0.0.0.0', port=5000)