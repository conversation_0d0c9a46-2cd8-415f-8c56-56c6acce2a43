# app/api/device_routes.py
# -*- coding: utf-8 -*-

from functools import wraps
from flask import Blueprint, request, jsonify, g
from app.services import device_service, auth_service
from app.models import User

device_blueprint = Blueprint('device_api', __name__)


# --- Token验证装饰器 ---
def token_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = None
        if 'Authorization' in request.headers:
            # 提取 Bearer Token
            token = request.headers['Authorization'].split(" ")[1]

        if not token:
            return jsonify({'error': '未提供认证Token'}), 401

        user = auth_service.verify_auth_token(token)
        if not user:
            return jsonify({'error': 'Token无效或已过期'}), 401

        # 将当前用户存入g对象，方便后续路由使用
        g.current_user = user

        return f(*args, **kwargs)

    return decorated_function


@device_blueprint.route('', methods=['POST'])
@token_required
def register_device():
    """注册新设备接口"""
    data = request.get_json()
    # 简单的验证
    if not data or not data.get('nickname') or not data.get('board_model'):
        return jsonify({"error": "缺少必要的设备信息"}), 400

    # g.current_user 是由 token_required 装饰器注入的
    device = device_service.register_device(g.current_user, data)

    if not device:
        return jsonify({"error": "设备注册失败"}), 500

    return jsonify({
        "message": "设备注册成功",
        "device": {
            "internal_device_id": device.internal_device_id,
            "nickname": device.nickname,
            "board_model": device.board_model
        }
    }), 201


@device_blueprint.route('', methods=['GET'])
@token_required
def get_devices():
    """获取当前用户的所有设备列表"""
    user_devices = device_service.get_user_devices(g.current_user)

    devices_list = [{
        "internal_device_id": dev.internal_device_id,
        "nickname": dev.nickname,
        "board_model": dev.board_model,
        "cloud_platform": dev.cloud_platform
    } for dev in user_devices]

    return jsonify(devices_list), 200