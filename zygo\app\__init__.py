# app/__init__.py
# -*- coding: utf-8 -*-

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from config import config

db = SQLAlchemy()


def create_app(config_name='default'):
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)

    db.init_app(app)

    # 注册蓝图
    from .api.auth_routes import auth_blueprint
    app.register_blueprint(auth_blueprint, url_prefix='/api/v1/auth')

    from .api.device_routes import device_blueprint
    app.register_blueprint(device_blueprint, url_prefix='/api/v1/devices')

    # 【新增】注册工作流蓝图
    from .api.workflow_routes import workflow_blueprint
    app.register_blueprint(workflow_blueprint, url_prefix='/api/v1/workflows')

    return app