# app/langgraph_def/agent_state.py
# -*- coding: utf-8 -*-

from typing import TypedDict, List, Dict, Optional, Any

class AgentState(TypedDict):
    """
    定义了整个工作流图的状态，兼容V3版API和原始example.py的核心逻辑。
    """
    # --- V3 API 引入的新字段 ---
    workflow_id: str             # 工作流的唯一ID
    user_id: Optional[int]       # 关联的用户ID
    status: str                  # 整个工作流的宏观状态 (e.g., RUNNING, PAUSED)
    current_step_name: str       # 用于前端展示的当前步骤名
    available_actions: List[str] # 当前用户可执行的操作列表
    log_snippet: str             # 向前端展示的日志片段
    workspace_path: str          # 此工作流的独立工作区路径
    user_action: Optional[str]   # 接收来自用户的操作指令

    # --- 从 example.py 迁移的核心字段 ---
    user_input: str              # 存储最原始的用户需求
    system_plan: Optional[Any]
    device_tasks_queue: List[Any]
    current_device_task: Optional[Any]
    current_api_spec: Optional[str]
    module_tasks: List[Any]
    current_module_task: Optional[Any]
    completed_modules: Dict[str, Any]
    feedback: str                # 节点间反馈，特别是用于错误修复
    project_files: Dict[str, Dict[str, str]]
    test_plan: Optional[Dict]
    original_module_plan: Optional[List[Any]]
    build_dir: str
    firmware_path: Optional[str]
    deployment_choice: Optional[str]
    dp_info_list: List[Dict[str, str]]