# workflow_test_script.py
# -*- coding: utf-8 -*-

import json
import urllib.request
import time
import sys

# --- 配置 ---
BASE_URL = "http://127.0.0.1:5000/api/v1"
USER_CREDENTIALS = {
    "username": f"testuser_{int(time.time())}",  # Use a unique username each time
    "password": "password123"
}
# V3 API
WORKFLOW_PAYLOAD = {
    "project_name": "智能光照传感器",
    "internal_device_id": None,  # Will be filled in after device registration
    "peripherals": [
        {"name": "模拟光照传感器", "pin": 34}
    ],
    "description": "一款连接到涂鸦云的光照传感器，能上报光照强度(illumination)，并能通过云端开关(enable_report)控制是否上报。"
}


def make_request(url, data=None, headers={}, method=None):
    """一个通用的请求函数，支持指定方法(如PUT)"""
    # 如果没有显式提供方法，则根据是否有data判断是GET还是POST
    if method is None:
        method = 'POST' if data else 'GET'

    req = urllib.request.Request(url, headers=headers, method=method)

    if data:
        req.data = json.dumps(data).encode('utf-8')
        req.add_header('Content-Type', 'application/json')

    try:
        with urllib.request.urlopen(req) as response:
            print(f"请求 {method} {url} 成功: {response.status} {response.reason}")
            response_body = response.read().decode('utf-8')
            # Handle empty response body
            if not response_body:
                return {}
            return json.loads(response_body)
    except urllib.error.HTTPError as e:
        print(f"请求 {method} {url} 失败: {e.code} {e.reason}")
        error_details = e.read().decode('utf-8')
        print(f"错误详情: {error_details}")
        # 在关键步骤失败时退出脚本
        if e.code >= 400:
            sys.exit(f"关键步骤失败，测试中止。")
        return None


def run_workflow_test():
    """执行完整的API测试流程"""
    access_token = None

    # --- 阶段一: 准备工作 (登录和注册设备) ---
    print("\\n--- 1. 注册新用户 ---")
    make_request(f"{BASE_URL}/auth/register", data=USER_CREDENTIALS)

    print("\\n--- 2. 登录并获取Token ---")
    login_response = make_request(f"{BASE_URL}/auth/login", data=USER_CREDENTIALS)
    if login_response and 'access_token' in login_response:
        access_token = login_response['access_token']
        print(f"成功获取Token!")
    else:
        return

    auth_headers = {"Authorization": f"Bearer {access_token}"}

    print("\\n--- 3. 注册一个新设备 ---")
    device_payload = {"nickname": "测试用的ESP32", "board_model": "esp32dev"}
    device_response = make_request(f"{BASE_URL}/devices", data=device_payload, headers=auth_headers)

    # 从响应中获取系统生成的设备ID
    internal_device_id = device_response.get("device", {}).get("internal_device_id")
    if not internal_device_id:
        print("注册设备失败，无法获取 internal_device_id，测试中止。")
        return
    print(f"设备注册成功, ID: {internal_device_id}")

    # --- 阶段二: 核心工作流测试 ---
    print("\\n--- 4. 启动工作流 ---")
    WORKFLOW_PAYLOAD["internal_device_id"] = internal_device_id
    start_response = make_request(f"{BASE_URL}/workflows", data=WORKFLOW_PAYLOAD, headers=auth_headers)
    workflow_id = start_response.get("workflow_id")
    if not workflow_id:
        print("启动工作流失败，测试中止。")
        return
    print(f"工作流已启动, ID: {workflow_id}")

    print("\\n--- 5. 开始轮询工作流状态... ---")
    file_content_printed = False
    while True:
        time.sleep(3)  # 每3秒轮询一次
        status_response = make_request(f"{BASE_URL}/workflows/{workflow_id}", headers=auth_headers)
        if not status_response:
            break

        status = status_response.get("status")
        step_name = status_response.get("current_step", {}).get("name")
        log_snippet = status_response.get("log_snippet", "N/A")
        print(f"  [状态: {status}] [当前步骤: {step_name}] - {log_snippet}")

        # 当文件生成后，尝试读取一次文件内容
        if not file_content_printed:
            files_tree = make_request(f"{BASE_URL}/workflows/{workflow_id}/files", headers=auth_headers)
            if files_tree:  # 检查是否返回了非空的文件树
                print("\\n  --- 检测到文件已生成, 正在读取 src/app_main.ino ---")
                file_content_response = make_request(f"{BASE_URL}/workflows/{workflow_id}/files?path=src/app_main.ino",
                                                     headers=auth_headers)
                if file_content_response and "content" in file_content_response:
                    print("-" * 20 + " 文件内容 " + "-" * 20)
                    print(file_content_response["content"][:300] + "...")  # 打印前300个字符
                    print("-" * 52)
                    file_content_printed = True

        # 检查是否需要用户交互
        if status == "PAUSED":
            available_actions = status_response.get("available_actions", [])
            print(f"  检测到工作流暂停，可用操作: {available_actions}")
            if available_actions:
                # 简单起见，我们总是选择第一个可用操作
                action_to_take = available_actions[0]
                print(f"  --- 自动执行操作: {action_to_take} ---")
                action_payload = {"action": action_to_take}
                make_request(f"{BASE_URL}/workflows/{workflow_id}/actions", data=action_payload, headers=auth_headers)
            else:
                print("  工作流暂停但无可用操作，可能出现问题，测试中止。")
                break

        # 检查是否结束
        if status in ["COMPLETED", "FAILED"]:
            print(f"\\n--- 工作流结束，最终状态: {status} ---")
            break

    print("\\n--- 工作流测试脚本执行完毕 ---")


if __name__ == "__main__":
    run_workflow_test()