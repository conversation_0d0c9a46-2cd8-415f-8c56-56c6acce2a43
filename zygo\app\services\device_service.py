# app/services/device_service.py
# -*- coding: utf-8 -*-

from app import db
from app.models import Device, User

def register_device(user: User, device_data: dict) -> Device:
    """为指定用户创建新设备"""
    new_device = Device(
        owner=user,
        nickname=device_data.get('nickname'),
        board_model=device_data.get('board_model'),
        cloud_platform=device_data.get('cloud_platform', 'tuya'),
        cloud_product_id=device_data.get('cloud_credentials', {}).get('product_id'),
        cloud_device_id=device_data.get('cloud_credentials', {}).get('device_id'),
        cloud_device_secret=device_data.get('cloud_credentials', {}).get('device_secret')
    )
    db.session.add(new_device)
    db.session.commit()
    return new_device

def get_user_devices(user: User):
    """获取指定用户的所有设备"""
    return Device.query.filter_by(user_id=user.id).all()