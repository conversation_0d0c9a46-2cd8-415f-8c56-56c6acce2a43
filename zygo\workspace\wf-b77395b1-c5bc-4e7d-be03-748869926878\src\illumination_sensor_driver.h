#ifndef ILLUMINATION_SENSOR_DRIVER_H
#define ILLUMINATION_SENSOR_DRIVER_H

class IlluminationSensor {
public:
    void illumination_sensor_init(int pin);
    int illumination_sensor_read_raw();
    float illumination_sensor_read_lux();
    void illumination_sensor_calibrate(int min_raw, int max_raw);
    void illumination_sensor_set_reference_voltage(float voltage);
    void illumination_sensor_set_resolution(int resolution);

private:
    int sensor_pin;
    int min_raw_value;
    int max_raw_value;
    float reference_voltage;
    int adc_resolution;
};

#endif // ILLUMINATION_SENSOR_DRIVER_H