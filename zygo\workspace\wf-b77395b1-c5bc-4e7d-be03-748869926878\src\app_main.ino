#include "tuya_handler.h"
#include "illumination_sensor_driver.h"
#include <ArduinoJson.h>

#define ILLUMINATION_SENSOR_PIN 34

WiFiClientSecure wifiClient;
PubSubClient mqttClient(wifiClient);
IlluminationSensor illuminationSensor;

bool enable_report = true;

void handle_tuya_commands(String &topic, String &payload) {
    StaticJsonDocument<256> doc;
    deserializeJson(doc, payload);

    if (doc["data"].containsKey("enable_report")) {
        enable_report = doc["data"]["enable_report"];
    }
}

void setup() {
    Serial.begin(115200);
    illuminationSensor.illumination_sensor_init(ILLUMINATION_SENSOR_PIN);
    tuya_setup(wifiClient, mqttClient, handle_tuya_commands);
}

void loop() {
    tuya_loop();

    static unsigned long lastPublishTime = 0;
    if (millis() - lastPublishTime > 10000) {
        lastPublishTime = millis();
        if (enable_report) {
            float lux = illuminationSensor.illumination_sensor_read_lux();

            char json_payload[128];
            sprintf(json_payload, "{\"data\":{\"illumination\":%.2f}}", lux);

            tuya_publish_data(String(json_payload));
        }
    }
}