
#include "SHA256.h"
SHA256::SHA256() {
    mbedtls_sha256_init(&ctx);
    mbedtls_sha256_starts_ret(&ctx, 0);
}
void SHA256::update(const void *data, size_t len) {
    if (len == 0) return;
    mbedtls_sha256_update_ret(&ctx, (const unsigned char *)data, len);
}
void SHA256::finalize(byte *hash) {
    mbedtls_sha256_finish_ret(&ctx, hash);
    mbedtls_sha256_starts_ret(&ctx, 0);
}
String SHA256::toString(const byte* hash, int len) {
    char hex_string[len * 2 + 1];
    for (int i = 0; i < len; i++) {
        sprintf(&hex_string[i * 2], "%02x", hash[i]);
    }
    hex_string[len * 2] = '\0';
    return String(hex_string);
}
