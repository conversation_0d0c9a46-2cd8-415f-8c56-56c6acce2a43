
#include "tuya_handler.h"
#include "config_manager.h"
#include <ArduinoJson.h>
#include <WiFi.h>
#include <time.h>
#include "SHA256.h"
static const char tuya_ca_cert[] PROGMEM = R"EOF(
-----BEGIN CERTIFICATE-----
MIIDxTCCAq2gAwIBAgIBADANBgkqhkiG9w0BAQsFADCBgzELMAkGA1UEBhMCVVMx
EDAOBgNVBAgTB0FyaXpvbmExEzARBgNVBAcTClNjb3R0c2RhbGUxGjAYBgNVBAoT
EUdvRGFkZHkuY29tLCBJbmMuMTEwLwYDVQQDEyhHbyBEYWRkeSBSb290IENlcnRp
ZmljYXRlIEF1dGhvcml0eSAtIEcyMB4XDTA5MDkwMTAwMDAwMFoXDTM3MTIzMTIz
NTk1OVowgYMxCzAJBgNVBAYTAlVTMRAwDgYDVQQIEwdBcml6b25hMRMwEQDVQQH
EwpTY290dHNkYWxlMRowGAYDVQQKExFHb0RhZGR5LmNvbSwgSW5jLjExMC8GA1UE
AxMoR28gRGFkZHkgUm9vdCBDZXJ0aWZpY2F0ZSBBdXRob3JpdHkgLSBHMjCCASIw
DQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAL9xYgjx+lk09xvJGKP3gElY6SKD
E6bFIEMBO4Tx5oVJnyfq9oQbTqC023CYxzIBsQU+B07u9PpPL1kwIuerGVZr4oAH
/PMWdYA5UXvl+TW2dE6pjYIT5LY/qQOD+qK+ihVqf94Lw7YZFAXK6sOoBJQ7Rnwy
DfMAZiLIjWltNowRGLfTshxgtDj6AozO091GB94KPutdfMh8+7ArU6SSYmlRJQVh
GkSBjCypQ5Yj36w6gZoOKcUcqeldHraenjAKOc7xiID7S13MMuyFYkMlNAJWJwGR
tDtwKj9useiciAF9n9T521NtYJ2/LOdYq7hfRvzOxBsDPAnrSTFcaUaz4EcCAwEA
AaNCMEAwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAQYwHQYDVR0OBBYE
FDqahQcQZyi27/a9BUFuIMGU2g/eMA0GCSqGSIb3DQEBCwUAA4IBAQCZ21151fmX
WWcDYfF+OwYxdS2hII5PZYe096acvNjpL9DbWu7PdIxztDhC2gV7+AJ1uP2lsdeu
9tfeE8tTEH6KRtGX+rcuKxGrkLAngPnon1rpN5+r5N9ss4UXnT3ZJE95kTXWXwTr
gIOrmgIttRD02JDHBHNA7XIloKmf7J6raBKZV8aPEjoJpL1E/QYVN8Gb5DKj7Tjo
2GTzLH4U/ALqn83/B2gX2yKQOC16jdFU8WnjXzPKej17CuPKf1855eJ1usV2GDPO
LPAvTK33sefOT6jEm0pUBsV/fdUID+Ic/n4XuKxe9tQWskMJDE32p2u0mYRlynqI
4uJEvlz36hz1
-----END CERTIFICATE-----
)EOF";
static WiFiClientSecure* _wifiClient;
static PubSubClient* _mqttClient;
static TuyaAppCallback _app_callback = nullptr;
static const char* mqtt_broker = "m1.tuyacn.com";
static const int mqtt_port = 8883;
static char clientID[128];
static char username[128];
static char password[128];
static char deviceId[50];
static char deviceSecret[50];
static String hmac256(const char* key, size_t key_len, const char* message, size_t msg_len);
static void tuya_mqtt_auth_signature_calculate();
static void connectToWiFi();
static void syncTime();
static void connectToMQTT();
static void internal_mqtt_callback(char *topic, byte *payload, unsigned int length);
void tuya_setup(WiFiClientSecure& wifiClient, PubSubClient& mqttClient, TuyaAppCallback app_callback) {
    _wifiClient = &wifiClient;
    _mqttClient = &mqttClient;
    _app_callback = app_callback;
    strcpy(deviceId, TUYA_DEVICE_ID);
    strcpy(deviceSecret, TUYA_DEVICE_SECRET);
    _wifiClient->setCACert(tuya_ca_cert);
    connectToWiFi();
    syncTime();
    _mqttClient->setServer(mqtt_broker, mqtt_port);
    _mqttClient->setCallback(internal_mqtt_callback);
}
void tuya_loop() {
    if (!_mqttClient->connected()) {
        connectToMQTT();
    }
    _mqttClient->loop();
}
bool tuya_publish_data(const String& data_json_string) {
    if (!_mqttClient->connected()) {
        return false;
    }
    char topic[128];
    sprintf(topic, "tylink/%s/thing/property/report", deviceId);
    return _mqttClient->publish(topic, data_json_string.c_str());
}
static String hmac256(const char* key, size_t key_len, const char* message, size_t msg_len) {
    SHA256 sha;
    byte k_ipad[64], k_opad[64];
    memset(k_ipad, 0, sizeof(k_ipad));
    memset(k_opad, 0, sizeof(k_opad));
    memcpy(k_ipad, key, key_len);
    memcpy(k_opad, key, key_len);
    for (int i = 0; i < 64; i++) {
        k_ipad[i] ^= 0x36;
        k_opad[i] ^= 0x5c;
    }
    sha.update(k_ipad, sizeof(k_ipad));
    sha.update(message, msg_len);
    byte hmac[32];
    sha.finalize(hmac);
    sha.update(k_opad, sizeof(k_opad));
    sha.update(hmac, sizeof(hmac));
    sha.finalize(hmac);
    return SHA256::toString(hmac);
}
static void tuya_mqtt_auth_signature_calculate() {
    long int t = time(NULL);
    sprintf(clientID, "tuyalink_%s", deviceId);
    sprintf(username, "%s|signMethod=hmacSha256,timestamp=%ld,secureMode=1,accessType=1", deviceId, t);
    String sign_content = String("deviceId=") + deviceId + ",timestamp=" + t + ",secureMode=1,accessType=1";
    String pass_hash = hmac256(deviceSecret, strlen(deviceSecret), sign_content.c_str(), sign_content.length());
    strcpy(password, pass_hash.c_str());
}
static void connectToWiFi() {
    WiFi.mode(WIFI_STA);
    WiFi.begin(WIFI_SSID, WIFI_PASSWORD);
    Serial.print("Connecting to WiFi");
    while (WiFi.status() != WL_CONNECTED) {
        delay(500);
        Serial.print(".");
    }
    Serial.println("\nWiFi connected. IP: " + WiFi.localIP().toString());
}
static void syncTime() {
    configTime(8 * 3600, 0, "pool.ntp.org", "time.windows.com");
    Serial.print("Waiting for NTP time sync");
    while (time(NULL) < 8 * 3600 * 2) {
        Serial.print(".");
        delay(1000);
    }
    Serial.println("\nTime synced.");
}
static void connectToMQTT() {
    while (!_mqttClient->connected()) {
        Serial.println("Attempting Tuya MQTT connection...");
        tuya_mqtt_auth_signature_calculate();
        if (_mqttClient->connect(clientID, username, password)) {
            Serial.println("Tuya MQTT connected.");
            char topic_sub[128];
            sprintf(topic_sub, "tylink/%s/thing/property/set", deviceId);
            _mqttClient->subscribe(topic_sub);
            Serial.println(String("Subscribed to: ") + topic_sub);
        } else {
            Serial.print("failed, rc=");
            Serial.print(_mqttClient->state());
            Serial.println(" try again in 5 seconds");
            delay(5000);
        }
    }
}
static void internal_mqtt_callback(char *topic, byte *payload, unsigned int length) {
    String topicStr(topic), payloadStr;
    for (unsigned int i = 0; i < length; i++) payloadStr += (char)payload[i];
    Serial.println("Tuya Handler received message. Topic: " + topicStr);
    Serial.println("Payload: " + payloadStr);
    if (_app_callback) _app_callback(topicStr, payloadStr);
}
