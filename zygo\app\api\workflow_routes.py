# app/api/workflow_routes.py
# -*- coding: utf-8 -*-

from flask import Blueprint, request, jsonify, g
from app.services import workflow_service
from .device_routes import token_required  # 从兄弟模块导入token_required装饰器

workflow_blueprint = Blueprint('workflow_api', __name__)


@workflow_blueprint.route('', methods=['POST'])
@token_required
def create_workflow():
    data = request.get_json()
    if not data or not data.get('internal_device_id') or not data.get('description'):
        return jsonify({"error": "Missing required fields"}), 400

    try:
        # g.current_user 是由 token_required 注入的
        initial_status = workflow_service.start_workflow(g.current_user.id, data)
        return jsonify(initial_status), 202
    except ValueError as e:
        return jsonify({"error": str(e)}), 404
    except Exception as e:
        return jsonify({"error": f"Internal server error: {e}"}), 500


@workflow_blueprint.route('/<workflow_id>', methods=['GET'])
@token_required
def get_status(workflow_id):
    try:
        status = workflow_service.get_workflow_status(workflow_id)
        return jsonify(status), 200
    except ValueError as e:
        return jsonify({"error": str(e)}), 404


@workflow_blueprint.route('/<workflow_id>/actions', methods=['POST'])
@token_required
def perform_action(workflow_id):
    data = request.get_json()
    if not data or not data.get('action'):
        return jsonify({"error": "Action not specified"}), 400

    try:
        workflow_service.post_workflow_action(workflow_id, data)
        return jsonify({"message": "Action accepted"}), 202
    except ValueError as e:
        return jsonify({"error": str(e)}), 404


# --- 文件操作API ---

@workflow_blueprint.route('/<workflow_id>/files', methods=['GET'])
@token_required
def list_files(workflow_id):
    path = request.args.get('path')
    try:
        if path:  # 获取文件内容
            content = workflow_service.get_file_content(workflow_id, path)
            return jsonify({"path": path, "content": content}), 200
        else:  # 获取文件树
            tree = workflow_service.get_file_tree(workflow_id)
            return jsonify(tree), 200
    except (ValueError, FileNotFoundError) as e:
        return jsonify({"error": str(e)}), 404


@workflow_blueprint.route('/<workflow_id>/files', methods=['PUT'])
@token_required
def update_file(workflow_id):
    path = request.args.get('path')
    data = request.get_json()
    if not path or not data or 'content' not in data:
        return jsonify({"error": "Missing path or content"}), 400

    try:
        workflow_service.save_file_content(workflow_id, path, data['content'])
        return jsonify({"message": f"File '{path}' saved successfully."}), 200
    except (ValueError, FileNotFoundError) as e:
        return jsonify({"error": str(e)}), 404