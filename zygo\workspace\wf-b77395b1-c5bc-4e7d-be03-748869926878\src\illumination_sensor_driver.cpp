#include "illumination_sensor_driver.h"

void IlluminationSensor::illumination_sensor_init(int pin) {
    sensor_pin = pin;
    min_raw_value = 0;
    max_raw_value = 1023;
    reference_voltage = 5.0;
    adc_resolution = 10;
    pinMode(sensor_pin, INPUT);
}

int IlluminationSensor::illumination_sensor_read_raw() {
    return analogRead(sensor_pin);
}

float IlluminationSensor::illumination_sensor_read_lux() {
    int raw_value = illumination_sensor_read_raw();
    return (float)(raw_value - min_raw_value) * (max_raw_value - min_raw_value) / (max_raw_value - min_raw_value);
}

void IlluminationSensor::illumination_sensor_calibrate(int min_raw, int max_raw) {
    min_raw_value = min_raw;
    max_raw_value = max_raw;
}

void IlluminationSensor::illumination_sensor_set_reference_voltage(float voltage) {
    reference_voltage = voltage;
}

void IlluminationSensor::illumination_sensor_set_resolution(int resolution) {
    adc_resolution = resolution;
    analogReadResolution(resolution);
}