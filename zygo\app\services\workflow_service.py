# app/services/workflow_service.py
# -*- coding: utf-8 -*-

import threading
import uuid
import shutil
from pathlib import Path

from app.langgraph_def.graph_builder import build_graph
from app.langgraph_def.agent_state import AgentState
from app.models import Device
from app import db
import time

# 使用线程安全的字典来管理所有正在运行的工作流
WORKFLOWS = {}
WORKFLOW_LOCK = threading.Lock()


def _run_graph_in_thread(workflow_id: str, initial_state: AgentState):
    """在后台线程中执行LangGraph工作流"""
    try:
        # 获取已编译的图
        graph = build_graph()

        # 定义配置
        config = {"recursion_limit": 100, "configurable": {"thread_id": workflow_id}}

        # 流式执行图，并在每一步后更新状态
        for step in graph.stream(initial_state, config):
            step_name, state_update = next(iter(step.items()))

            with WORKFLOW_LOCK:
                # 更新工作流的最新状态
                WORKFLOWS[workflow_id]['latest_state'].update(state_update)
                WORKFLOWS[workflow_id]['latest_state']['current_step_name'] = step_name

                # 【核心】根据图的执行状态，决定工作流的宏观状态
                # 这里可以根据step_name或state_update中的特定字段来判断
                if step_name == "pre_deployment_pause":
                    WORKFLOWS[workflow_id]['status'] = "PAUSED"
                elif step_name == "__end__":
                    WORKFLOWS[workflow_id]['status'] = "COMPLETED"
                else:
                    WORKFLOWS[workflow_id]['status'] = "RUNNING"

    except Exception as e:
        print(f"Workflow thread {workflow_id} crashed: {e}")
        with WORKFLOW_LOCK:
            WORKFLOWS[workflow_id]['status'] = "FAILED"
            WORKFLOWS[workflow_id]['latest_state']['feedback'] = str(e)


def start_workflow(user_id: int, request_data: dict) -> dict:
    """创建并启动一个新的工作流"""
    workflow_id = f"wf-{uuid.uuid4()}"

    # 1. 从数据库获取设备信息
    device = Device.query.filter_by(internal_device_id=request_data['internal_device_id'], user_id=user_id).first()
    if not device:
        raise ValueError("Device not found or does not belong to the user.")

    # 2. 创建独立的工作区
    workspace_path = Path("workspace") / workflow_id
    workspace_path.mkdir(parents=True, exist_ok=True)

    # 3. 【适配器逻辑】构建符合LangGraph要求的初始状态
    # 我们手动构建 system_plan 和 current_device_task，跳过图中废弃的节点
    initial_state = AgentState(
        workflow_id=workflow_id,
        user_id=user_id,
        status="RUNNING",
        current_step_name="module_architect",
        user_input=request_data['description'],  # 将API的description映射到user_input

        # 手动构建的设备任务
        current_device_task={
            "device_id": device.internal_device_id,
            "board": device.board_model,
            "description": request_data['description'],
            "peripherals": request_data['peripherals'],
            "communication": {}  # 可根据需求扩展
        },

        # 预设工作流所需其他字段
        workspace_path=str(workspace_path.resolve()),
        module_tasks=[],
        completed_modules={},
        project_files={},
        feedback="",

        # V3 API 新增字段
        available_actions=[],
        log_snippet="[INFO] Workflow started. Designing modules...",
        # 其他AgentState字段...
    )

    # 4. 在全局字典中注册工作流
    with WORKFLOW_LOCK:
        WORKFLOWS[workflow_id] = {
            "status": "STARTING",
            "thread": None,
            "latest_state": initial_state,
            "user_event": threading.Event()  # 用于暂停/恢复的事件
        }

    # 5. 启动后台线程执行
    thread = threading.Thread(target=_run_graph_in_thread, args=(workflow_id, initial_state))
    thread.daemon = True
    thread.start()

    with WORKFLOW_LOCK:
        WORKFLOWS[workflow_id]['thread'] = thread

    # 6. 遵循V3 API规约，立即返回初始状态
    return {
        "workflow_id": workflow_id,
        "status": "RUNNING",
        "current_step": {"name": "模块规划"},
        "available_actions": [],
        "log_snippet": "[INFO] 工作流已启动，正在进行模块规划..."
    }


def get_workflow_status(workflow_id: str) -> dict:
    """获取指定工作流的最新状态"""
    with WORKFLOW_LOCK:
        workflow = WORKFLOWS.get(workflow_id)
        if not workflow:
            raise ValueError("Workflow not found.")

        status = workflow['status']
        state = workflow['latest_state']

        # 【核心】根据状态动态生成 available_actions
        available_actions = []
        if status == "PAUSED":
            # 这是在 pre_deployment_pause 节点设置的状态
            available_actions.extend(["DEPLOY_USB", "DEPLOY_OTA"])

        # 格式化输出以符合API规约
        response = {
            "workflow_id": workflow_id,
            "status": status,
            "current_step": {"name": state.get('current_step_name', 'N/A')},
            "available_actions": available_actions,
            "log_snippet": state.get('feedback', '') or f"[INFO] Last updated at {time.ctime()}",
            "project_files": state.get('project_files', {})  # 也可以在这里返回文件信息
        }
        return response


def post_workflow_action(workflow_id: str, action_data: dict):
    """对工作流执行一个动作，例如恢复"""
    action = action_data.get("action")
    with WORKFLOW_LOCK:
        workflow = WORKFLOWS.get(workflow_id)
        if not workflow:
            raise ValueError("Workflow not found.")

        # 将用户的选择注入到工作流状态中
        state = workflow['latest_state']
        if action == "DEPLOY_USB":
            state['user_action'] = "DEPLOY_USB"
            state['deployment_choice'] = "manual"
        elif action == "DEPLOY_OTA":
            state['user_action'] = "DEPLOY_OTA"
            state['deployment_choice'] = "ota"

        # 唤醒后台线程 (此简化版中，图是自动流转的，更复杂的暂停/恢复需要使用Event)
        # 在我们的图中，pre_deployment_pause后会自动进入下一步，
        # 下一步的route_deployment会读取我们刚刚注入的user_action
        print(f"Action '{action}' received for workflow {workflow_id}. Graph will proceed.")


# --- 文件操作服务 ---
def get_file_tree(workflow_id: str) -> list:
    with WORKFLOW_LOCK:
        if workflow_id not in WORKFLOWS:
            raise ValueError("Workflow not found.")
        workspace_path = Path(WORKFLOWS[workflow_id]['latest_state']['workspace_path'])

    if not workspace_path.is_dir():
        return []

    def build_tree(current_path: Path):
        tree = []
        for item in sorted(current_path.iterdir()):
            node = {"name": item.name}
            if item.is_dir():
                node['type'] = 'folder'
                node['children'] = build_tree(item)
            else:
                node['type'] = 'file'
            tree.append(node)
        return tree

    return build_tree(workspace_path)


def get_file_content(workflow_id: str, file_path: str) -> str:
    with WORKFLOW_LOCK:
        if workflow_id not in WORKFLOWS:
            raise ValueError("Workflow not found.")
        workspace_path = Path(WORKFLOWS[workflow_id]['latest_state']['workspace_path'])

    # 安全性检查
    full_path = (workspace_path / file_path).resolve()
    if workspace_path.resolve() not in full_path.parents:
        raise ValueError("Access denied: path is outside of workspace.")

    return full_path.read_text(encoding='utf-8')


def save_file_content(workflow_id: str, file_path: str, content: str):
    with WORKFLOW_LOCK:
        if workflow_id not in WORKFLOWS:
            raise ValueError("Workflow not found.")
        workspace_path = Path(WORKFLOWS[workflow_id]['latest_state']['workspace_path'])

    # 安全性检查
    full_path = (workspace_path / file_path).resolve()
    if workspace_path.resolve() not in full_path.parents:
        raise ValueError("Access denied: path is outside of workspace.")

    full_path.write_text(content, encoding='utf-8')