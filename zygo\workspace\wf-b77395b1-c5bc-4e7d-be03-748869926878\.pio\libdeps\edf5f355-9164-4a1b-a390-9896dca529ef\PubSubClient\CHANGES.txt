2.8
   * Add setBufferSize() to override MQTT_MAX_PACKET_SIZE
   * Add setKeepAlive() to override MQTT_KEEPALIVE
   * Add setSocketTimeout() to overide MQTT_SOCKET_TIMEOUT
   * Added check to prevent subscribe/unsubscribe to empty topics
   * Declare wifi mode prior to connect in ESP example
   * Use `strnlen` to avoid overruns
   * Support pre-connected Client objects

2.7
   * Fix remaining-length handling to prevent buffer overrun
   * Add large-payload API - beginPublish/write/publish/endPublish
   * Add yield call to improve reliability on ESP
   * Add Clean Session flag to connect options
   * Add ESP32 support for functional callback signature
   * Various other fixes

2.4
   * Add MQTT_SOCKET_TIMEOUT to prevent it blocking indefinitely
     whilst waiting for inbound data
   * Fixed return code when publishing >256 bytes

2.3
   * Add publish(topic,payload,retained) function

2.2
   * Change code layout to match Arduino Library reqs

2.1
   * Add MAX_TRANSFER_SIZE def to chunk messages if needed
   * Reject topic/payloads that exceed MQTT_MAX_PACKET_SIZE

2.0
   * Add (and default to) MQTT 3.1.1 support
   * Fix PROGMEM handling for Intel Galileo/ESP8266
   * Add overloaded constructors for convenience
   * Add chainable setters for server/callback/client/stream
   * Add state function to return connack return code

1.9
   * Do not split MQTT packets over multiple calls to _client->write()
   * API change: All constructors now require an instance of Client
      to be passed in.
   * Fixed example to match 1.8 api changes - dpslwk
   * Added username/password support - WilHall
   * Added publish_P - publishes messages from PROGMEM - jobytaffey

1.8
    * KeepAlive interval is configurable in PubSubClient.h
    * Maximum packet size is configurable in PubSubClient.h
    * API change: Return boolean rather than int from various functions
    * API change: Length parameter in message callback changed
       from int to unsigned int
    * Various internal tidy-ups around types
1.7
    * Improved keepalive handling
    * Updated to the Arduino-1.0 API
1.6
    * Added the ability to publish a retained message

1.5
    * Added default constructor
    * Fixed compile error when used with arduino-0021 or later

1.4
    * Fixed connection lost handling

1.3
    * Fixed packet reading bug in PubSubClient.readPacket

1.2
    * Fixed compile error when used with arduino-0016 or later


1.1
    * Reduced size of library
    * Added support for Will messages
    * Clarified licensing - see LICENSE.txt


1.0
    * Only Quality of Service (QOS) 0 messaging is supported
    * The maximum message size, including header, is 128 bytes
    * The keepalive interval is set to 30 seconds
    * No support for Will messages
