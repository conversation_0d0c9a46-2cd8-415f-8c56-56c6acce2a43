# app/models.py

import uuid
from werkzeug.security import generate_password_hash, check_password_hash
from . import db


class User(db.Model):
    __tablename__ = 'users'
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, index=True, nullable=False)
    password_hash = db.Column(db.String(256), nullable=False)  # 增加密码哈希字段长度

    # 用户的个人配置
    wifi_ssid = db.Column(db.String(64), nullable=True)  # [cite: 207]
    wifi_password = db.Column(db.String(64), nullable=True)  # [cite: 208]

    # 'User'和'Device'之间的一对多关系
    devices = db.relationship('Device', backref='owner', lazy='dynamic')  # [cite: 209]

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)


class Device(db.Model):
    __tablename__ = 'devices'
    id = db.Column(db.Integer, primary_key=True)  # [cite: 211]
    # 使用UUID确保系统生成的ID全局唯一
    internal_device_id = db.Column(db.String(36), unique=True, nullable=False,
                                   default=lambda: str(uuid.uuid4()))  # [cite: 212]
    nickname = db.Column(db.String(64), nullable=False)  # [cite: 213]
    board_model = db.Column(db.String(64), nullable=False)  # [cite: 214]

    # 云平台信息
    cloud_platform = db.Column(db.String(32), default='tuya')  # [cite: 215]
    cloud_product_id = db.Column(db.String(64), nullable=True)  # [cite: 216]
    cloud_device_id = db.Column(db.String(64), nullable=True)  # [cite: 217]
    cloud_device_secret = db.Column(db.String(64), nullable=True)  # [cite: 218]

    # 指向'User'模型的外键
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))  # [cite: 219]